"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { CreditsDisplay } from "@/components/ui/credits-display";
import { api } from "@/convex/_generated/api";
import { useMutation, useQuery } from "convex/react";
import { toast } from "sonner";
import { useState } from "react";
import React from "react";
import { useUser } from "@clerk/clerk-react";

import {
  Settings,
  Database,
  CreditCard,
  Star,
  Crown,
  MessageSquare,
  Clock,
  CheckCircle,
  AlertTriangle,
  Shield,
  Lock,
  Edit,
  Save,
  X,
  User,
  Building2,
  UserCircle,
} from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { UserPlanManager } from "@/components/admin/user-plan-manager";

export default function AdminPage() {
  const { user } = useUser();
  const [loading, setLoading] = useState<string | null>(null);
  const [editingAction, setEditingAction] = useState<any>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editForm, setEditForm] = useState({
    cost: 0,
    duration: 0,
    description: "",
    isActive: true,
  });

  // Trial form states
  const [trialDays, setTrialDays] = useState(15);
  const [premiumPrice, setPremiumPrice] = useState(99);
  const [trialEnabled, setTrialEnabled] = useState(true);
  const [extendTrialForm, setExtendTrialForm] = useState({
    userEmail: "",
    days: 15,
  });

  // ✅ TODOS los hooks al inicio - ANTES de cualquier return
  const currentUser = useQuery(api.users.getCurrentUser);
  
  // Mutations - SIEMPRE en el mismo orden
  const seedCreditActions = useMutation(api.creditActions.seedCreditActions);
  const expireServices = useMutation(api.transactions.expireServices);
  const updateCreditAction = useMutation(api.creditActions.updateCreditActionConfig);
  const migrateLegacyFeatured = useMutation(api.featuredProperties.migrateLegacyFeaturedProperties);
  const clearAllFeatured = useMutation(api.featuredProperties.clearAllFeaturedProperties);
  const cleanInconsistentData = useMutation(api.featuredProperties.cleanInconsistentData);
  const resetAllSubscriptions = useMutation(api.stripe.resetAllSubscriptionsToFree);
  const makeUserAdmin = useMutation(api.users.makeAdmin);

  // Trial mutations
  const updateAdminSetting = useMutation(api.admin.updateSetting);
  const extendUserTrial = useMutation(api.admin.extendUserTrial);

  // Queries - SIEMPRE en el mismo orden
  const creditActions = useQuery(api.creditActions.getAllCreditActions);
  const allTransactions = useQuery(api.transactions.getAllTransactionsWithUsers, { limit: 10, showAll: false });
  const featuredStatus = useQuery(api.featuredProperties.getUserFeaturedStatus);
  const expiringTransactions = useQuery(api.transactions.getExpiringTransactions, { daysAhead: 7 });
  const premiumProperty = useQuery(api.featuredProperties.getCurrentPremiumProperty);

  // Trial queries
  const adminSettings = useQuery(api.admin.getPublicSettings);
  const trialStats = useQuery(api.admin.getTrialStats);
  const allUsers = useQuery(api.admin.getAllUsers);

  // ✅ TODOS los useEffect al inicio también
  // Sync form values with settings
  React.useEffect(() => {
    if (adminSettings) {
      setTrialDays(adminSettings.trial_days || 15);
      setPremiumPrice(adminSettings.premium_price || 99);
      setTrialEnabled(adminSettings.trial_enabled ?? true);
    }
  }, [adminSettings]);

  // ✅ DESPUÉS de todos los hooks, hacer las verificaciones
  
  // Mostrar loading mientras se verifica el usuario
  if (currentUser === undefined) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-500">Verificando permisos...</p>
        </div>
      </div>
    );
  }

  // Verificar que el usuario existe y es admin
  if (!currentUser || currentUser.role !== 'admin') {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="max-w-md mx-auto text-center bg-white rounded-lg shadow-lg p-8">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Lock className="h-8 w-8 text-red-600" />
          </div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Acceso Denegado</h1>
          <p className="text-gray-600 mb-6">
            No tienes permisos para acceder al panel de administración. 
            Solo los administradores pueden ver esta página.
          </p>
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <div className="flex items-center gap-2">
              <Shield className="h-5 w-5 text-red-600" />
              <span className="text-sm text-red-800 font-medium">
                Rol requerido: Administrador
              </span>
            </div>
            <p className="text-sm text-red-700 mt-1">
              Tu rol actual: {currentUser?.role || 'Sin rol asignado'}
            </p>
          </div>
          <Button 
            onClick={() => window.history.back()} 
            variant="outline"
            className="w-full"
          >
            Volver Atrás
          </Button>
        </div>
      </div>
    );
  }

  // ✅ Handlers y lógica después de las verificaciones

  const handleSeedCredits = async () => {
    try {
      setLoading("seed");
      const result = await seedCreditActions();
      toast.success(`✅ ${result.message} (${result.count} configuraciones)`);
    } catch (error: any) {
      toast.error(`❌ Error: ${error.message}`);
    } finally {
      setLoading(null);
    }
  };

  const handleExpireServices = async () => {
    try {
      setLoading("expire");
      const result = await expireServices();
      toast.success(`✅ Servicios expirados: ${result.processed}`);
    } catch (error: any) {
      toast.error(`❌ Error: ${error.message}`);
    } finally {
      setLoading(null);
    }
  };

  const handleMigrateLegacy = async () => {
    try {
      setLoading("migrating");
      const result = await migrateLegacyFeatured();
      toast.success(`✅ ${result.message}`);
    } catch (error) {
      console.error("Error migrating legacy properties:", error);
      toast.error("Error al migrar propiedades legacy");
    } finally {
      setLoading(null);
    }
  };

  const handleClearAllFeatured = async () => {
    toast("⚠️ Confirmar limpieza de propiedades", {
      description: "Esto limpiará TODAS las propiedades destacadas y devolverá los créditos a los usuarios.",
      action: {
        label: "Confirmar",
        onClick: async () => {
          try {
            setLoading("clearing");
            const result = await clearAllFeatured();
            toast.success(`✅ ${result.message}`);
          } catch (error: any) {
            toast.error(`❌ Error: ${error.message}`);
          } finally {
            setLoading(null);
          }
        },
      },
      cancel: {
        label: "Cancelar",
        onClick: () => {
          toast.dismiss();
        },
      },
      duration: 10000,
    });
  };

  const handleCleanInconsistentData = async () => {
    toast("🧹 Confirmar limpieza de datos inconsistentes", {
      description: "Esto limpiará referencias a transacciones canceladas y fechas expiradas.",
      action: {
        label: "Confirmar",
        onClick: async () => {
          try {
            setLoading("cleaning-data");
            const result = await cleanInconsistentData();
            toast.success(`✅ ${result.message}`);
          } catch (error: any) {
            toast.error(`❌ Error: ${error.message}`);
          } finally {
            setLoading(null);
          }
        },
      },
      cancel: {
        label: "Cancelar",
        onClick: () => {
          toast.dismiss();
        },
      },
      duration: 10000,
    });
  };

  const handleResetSubscriptions = async () => {
    toast("🔄 Confirmar reset de suscripciones", {
      description: "Esto cambiará TODAS las suscripciones a plan gratuito. Solo para testing.",
      action: {
        label: "Confirmar Reset",
        onClick: async () => {
          try {
            setLoading("reset-subscriptions");
            const result = await resetAllSubscriptions();
            toast.success(`✅ ${result.message}`);
          } catch (error: any) {
            toast.error(`❌ Error: ${error.message}`);
          } finally {
            setLoading(null);
          }
        },
      },
      cancel: {
        label: "Cancelar",
        onClick: () => {
          toast.dismiss();
        },
      },
      duration: 10000,
    });
  };

  const handleEditAction = (action: any) => {
    setEditingAction(action);
    setEditForm({
      cost: action.cost,
      duration: action.duration || 0,
      description: action.description,
      isActive: action.isActive,
    });
    setDialogOpen(true);
  };

  const handleSaveEdit = async () => {
    if (!editingAction) return;
    
    try {
      setLoading("edit");
      await updateCreditAction({
        actionId: editingAction._id,
        cost: editForm.cost,
        duration: editForm.duration || undefined,
        description: editForm.description,
        isActive: editForm.isActive,
      });
      
      toast.success("✅ Configuración actualizada exitosamente");
      setEditingAction(null);
      setDialogOpen(false);
    } catch (error: any) {
      toast.error(`❌ Error: ${error.message}`);
    } finally {
      setLoading(null);
    }
  };

  const handleCancelEdit = () => {
    setEditingAction(null);
    setDialogOpen(false);
  };

  const getActionIcon = (action: string) => {
    switch (action) {
      case "featured_property":
        return <Star className="h-4 w-4 text-yellow-600" />;
      case "premium_home":
        return <Crown className="h-4 w-4 text-purple-600" />;
      case "message_inquiry":
      case "message_viewing":
      case "message_offer":
      case "message_negotiation":
        return <MessageSquare className="h-4 w-4 text-blue-600" />;
      default:
        return <CreditCard className="h-4 w-4 text-gray-600" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "active":
        return (
          <Badge className="bg-green-100 text-green-800">
            <CheckCircle className="h-3 w-3 mr-1" />
            Activo
          </Badge>
        );
      case "expired":
        return (
          <Badge className="bg-red-100 text-red-800">
            <Clock className="h-3 w-3 mr-1" />
            Expirado
          </Badge>
        );
      case "cancelled":
        return (
          <Badge className="bg-gray-100 text-gray-800">
            Cancelado
          </Badge>
        );
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const handleMakeAdmin = async () => {
    if (!currentUser?.email) {
      toast.error("No se puede obtener el email del usuario");
      return;
    }

    try {
      await makeUserAdmin({ email: currentUser.email });
      toast.success("Usuario convertido a administrador");
      // Refrescar la página para actualizar el rol
      window.location.reload();
    } catch (error) {
      console.error("Error:", error);
      toast.error("Error al convertir usuario a admin");
    }
  };

  // Trial handlers
  const handleUpdateTrialDays = async () => {
    try {
      setLoading("trial-days");
      await updateAdminSetting({
        key: "trial_days",
        value: trialDays,
      });
      toast.success(`✅ Días de trial actualizados a ${trialDays}`);
    } catch (error: any) {
      toast.error(`❌ Error: ${error.message}`);
    } finally {
      setLoading(null);
    }
  };

  const handleUpdatePremiumPrice = async () => {
    try {
      setLoading("premium-price");
      await updateAdminSetting({
        key: "premium_price",
        value: premiumPrice,
      });
      toast.success(`✅ Precio premium actualizado a $${premiumPrice}`);
    } catch (error: any) {
      toast.error(`❌ Error: ${error.message}`);
    } finally {
      setLoading(null);
    }
  };

  const handleToggleTrialEnabled = async () => {
    try {
      setLoading("trial-enabled");
      await updateAdminSetting({
        key: "trial_enabled",
        value: !trialEnabled,
      });
      setTrialEnabled(!trialEnabled);
      toast.success(`✅ Trial ${!trialEnabled ? 'habilitado' : 'deshabilitado'}`);
    } catch (error: any) {
      toast.error(`❌ Error: ${error.message}`);
    } finally {
      setLoading(null);
    }
  };

  const handleExtendTrial = async () => {
    if (!extendTrialForm.userEmail.trim()) {
      toast.error("Ingresa un email válido");
      return;
    }

    try {
      setLoading("extend-trial");
      await extendUserTrial({
        userEmail: extendTrialForm.userEmail,
        additionalDays: extendTrialForm.days,
      });
      toast.success(`✅ Trial extendido ${extendTrialForm.days} días para ${extendTrialForm.userEmail}`);
      setExtendTrialForm({ userEmail: "", days: 15 });
    } catch (error: any) {
      toast.error(`❌ Error: ${error.message}`);
    } finally {
      setLoading(null);
    }
  };

  return (
    <div className="flex flex-col gap-6 p-4 md:p-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl md:text-3xl font-semibold tracking-tight flex items-center gap-2">
            <Settings className="h-6 w-6 md:h-8 md:w-8" />
            Panel de Administración
          </h1>
          <p className="text-muted-foreground mt-2 text-sm md:text-base">
            Gestiona el sistema de créditos y servicios destacados
          </p>
        </div>
        
        <div className="flex-shrink-0">
          <CreditsDisplay variant="compact" />
        </div>
      </div>

      {/* Estado del Sistema */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <User className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Total Usuarios</p>
                <p className="text-2xl font-bold">{trialStats?.totalUsers || 0}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <Clock className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Trials Activos</p>
                <p className="text-2xl font-bold">{trialStats?.activeTrials || 0}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Crown className="h-5 w-5 text-purple-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Suscripciones</p>
                <p className="text-2xl font-bold">{trialStats?.paidSubscriptions || 0}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <AlertTriangle className="h-5 w-5 text-yellow-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Conversión</p>
                <p className="text-2xl font-bold">{trialStats?.conversionRate || 0}%</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Acciones del Sistema */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Acciones del Sistema
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
            <Button
              onClick={handleSeedCredits}
              disabled={loading === "seed"}
              className="bg-blue-600 hover:bg-blue-700 w-full"
            >
              {loading === "seed" ? "Procesando..." : "🌱 Seed Configuraciones"}
            </Button>

            <Button
              onClick={handleExpireServices}
              disabled={loading === "expire"}
              variant="outline"
              className="w-full"
            >
              {loading === "expire" ? "Procesando..." : "⏰ Expirar Servicios"}
            </Button>

            <Button
              onClick={handleMigrateLegacy}
              disabled={loading === "migrating"}
              variant="outline"
              className="w-full"
            >
              {loading === "migrating" ? "Migrando..." : "Migrar Propiedades Legacy"}
            </Button>

            <Button
              onClick={handleClearAllFeatured}
              disabled={loading === "clearing"}
              variant="outline"
              className="w-full"
            >
              {loading === "clearing" ? "Limpiando..." : "Limpiar Todas las Propiedades Destacadas"}
            </Button>

            <Button
              onClick={handleCleanInconsistentData}
              disabled={loading === "cleaning-data"}
              variant="outline"
              className="w-full"
            >
              {loading === "cleaning-data" ? "Limpiando..." : "Limpiar Datos Inconsistentes"}
            </Button>

            <Button
              onClick={handleResetSubscriptions}
              disabled={loading === "reset-subscriptions"}
              variant="outline"
              className="w-full bg-red-50 text-red-700 border-red-200 hover:bg-red-100"
            >
              {loading === "reset-subscriptions" ? "Reseteando..." : "🔄 Resetear Suscripciones"}
            </Button>
          </div>

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h4 className="font-medium text-blue-900 mb-2">ℹ️ Información:</h4>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• <strong>Seed Configuraciones:</strong> Crea los precios iniciales del sistema</li>
              <li>• <strong>Expirar Servicios:</strong> Ejecuta manualmente la limpieza de servicios</li>
              <li>• <strong>Migrar Legacy:</strong> Convierte propiedades destacadas del sistema viejo</li>
              <li>• <strong>Limpiar Destacadas:</strong> Quita TODAS las propiedades destacadas y devuelve créditos</li>
              <li>• <strong>Limpiar Datos Inconsistentes:</strong> Limpia referencias a transacciones canceladas y fechas expiradas</li>
              <li>• <strong>Resetear Suscripciones:</strong> Cambia TODAS las suscripciones a plan gratuito</li>
              <li>• <strong>Estado en tiempo real:</strong> Se actualiza automáticamente</li>
            </ul>
          </div>
        </CardContent>
      </Card>

      {/* Configuración de Trial y Precios */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Configuración de Trial */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Configuración de Trial
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="trial-days">Días de Trial</Label>
              <div className="flex gap-2">
                <Input
                  id="trial-days"
                  type="number"
                  min="1"
                  max="365"
                  value={trialDays}
                  onChange={(e) => setTrialDays(parseInt(e.target.value) || 15)}
                  className="flex-1"
                />
                <Button
                  onClick={handleUpdateTrialDays}
                  disabled={loading === "trial-days"}
                  size="sm"
                >
                  {loading === "trial-days" ? "..." : "Actualizar"}
                </Button>
              </div>
              <p className="text-sm text-muted-foreground">
                Días de trial gratuito para nuevos usuarios seller/agent
              </p>
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="trial-enabled">Trial Habilitado</Label>
                <p className="text-sm text-muted-foreground">
                  Activar/desactivar trial automático
                </p>
              </div>
              <Switch
                id="trial-enabled"
                checked={trialEnabled}
                onCheckedChange={handleToggleTrialEnabled}
                disabled={loading === "trial-enabled"}
              />
            </div>
          </CardContent>
        </Card>

        {/* Configuración de Precios */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="h-5 w-5" />
              Configuración de Precios
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="premium-price">Precio Premium (USD)</Label>
              <div className="flex gap-2">
                <Input
                  id="premium-price"
                  type="number"
                  min="1"
                  value={premiumPrice}
                  onChange={(e) => setPremiumPrice(parseInt(e.target.value) || 99)}
                  className="flex-1"
                />
                <Button
                  onClick={handleUpdatePremiumPrice}
                  disabled={loading === "premium-price"}
                  size="sm"
                >
                  {loading === "premium-price" ? "..." : "Actualizar"}
                </Button>
              </div>
              <p className="text-sm text-muted-foreground">
                Precio mensual de la suscripción premium
              </p>
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-medium text-blue-900 mb-2">💡 Configuración Actual:</h4>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• <strong>Trial:</strong> {trialDays} días {trialEnabled ? '(Activo)' : '(Desactivado)'}</li>
                <li>• <strong>Premium:</strong> ${premiumPrice}/mes</li>
                <li>• <strong>Créditos gratuitos:</strong> 10 por mes</li>
                <li>• <strong>Premium Home:</strong> 25 créditos</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Extender Trial Manual */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <UserCircle className="h-5 w-5" />
            Extender Trial Manual
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="user-email">Email del Usuario</Label>
              <Input
                id="user-email"
                type="email"
                placeholder="<EMAIL>"
                value={extendTrialForm.userEmail}
                onChange={(e) => setExtendTrialForm({...extendTrialForm, userEmail: e.target.value})}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="extend-days">Días Adicionales</Label>
              <Input
                id="extend-days"
                type="number"
                min="1"
                max="365"
                value={extendTrialForm.days}
                onChange={(e) => setExtendTrialForm({...extendTrialForm, days: parseInt(e.target.value) || 15})}
              />
            </div>
            <div className="space-y-2">
              <Label>&nbsp;</Label>
              <Button
                onClick={handleExtendTrial}
                disabled={loading === "extend-trial" || !extendTrialForm.userEmail.trim()}
                className="w-full"
              >
                {loading === "extend-trial" ? "Extendiendo..." : "Extender Trial"}
              </Button>
            </div>
          </div>
          <p className="text-sm text-muted-foreground mt-2">
            Extiende el trial de un usuario específico por casos especiales
          </p>
        </CardContent>
      </Card>

      {/* Configuraciones de Créditos - MEJORADO */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            Configuraciones de Créditos
            <div className="text-sm text-gray-500 font-normal">
              Haz clic en el ícono de editar para modificar
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {creditActions && creditActions.length > 0 ? (
            <div className="space-y-3">
              {creditActions.map((action) => (
                <div
                  key={action._id}
                  className="flex items-center justify-between p-4 bg-gray-50 rounded-lg border hover:bg-gray-100 transition-colors"
                >
                  <div className="flex items-center gap-3">
                    {getActionIcon(action.action)}
                    <div>
                      <p className="font-medium">{action.description}</p>
                      <p className="text-sm text-gray-600">
                        Acción: {action.action}
                        {action.duration && ` • Duración: ${action.duration} días`}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <Badge className="bg-blue-100 text-blue-800">
                      {action.cost} créditos
                    </Badge>
                    <Badge variant={action.isActive ? "default" : "secondary"}>
                      {action.isActive ? "Activo" : "Inactivo"}
                    </Badge>
                    <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
                      <DialogTrigger asChild>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEditAction(action)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="sm:max-w-[425px]">
                        <DialogHeader>
                          <DialogTitle>Editar Configuración</DialogTitle>
                          <DialogDescription>
                            Modifica los valores para: {editingAction?.description}
                          </DialogDescription>
                        </DialogHeader>
                        <div className="grid gap-4 py-4">
                          <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="cost" className="text-right">
                              Créditos
                            </Label>
                            <Input
                              id="cost"
                              type="number"
                              min="0"
                              value={editForm.cost}
                              onChange={(e) => setEditForm({...editForm, cost: parseInt(e.target.value) || 0})}
                              className="col-span-3"
                            />
                          </div>
                          {editingAction?.duration !== undefined && (
                            <div className="grid grid-cols-4 items-center gap-4">
                              <Label htmlFor="duration" className="text-right">
                                Días
                              </Label>
                              <Input
                                id="duration"
                                type="number"
                                min="0"
                                value={editForm.duration}
                                onChange={(e) => setEditForm({...editForm, duration: parseInt(e.target.value) || 0})}
                                className="col-span-3"
                              />
                            </div>
                          )}
                          <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="description" className="text-right">
                              Descripción
                            </Label>
                            <Input
                              id="description"
                              value={editForm.description}
                              onChange={(e) => setEditForm({...editForm, description: e.target.value})}
                              className="col-span-3"
                            />
                          </div>
                          <div className="flex items-center space-x-2">
                            <Switch
                              id="isActive"
                              checked={editForm.isActive}
                              onCheckedChange={(checked) => setEditForm({...editForm, isActive: checked})}
                            />
                            <Label htmlFor="isActive">Activo</Label>
                          </div>
                        </div>
                        <DialogFooter>
                          <Button
                            type="button"
                            variant="outline"
                            onClick={handleCancelEdit}
                          >
                            <X className="h-4 w-4 mr-2" />
                            Cancelar
                          </Button>
                          <Button 
                            type="submit" 
                            onClick={handleSaveEdit}
                            disabled={loading === "edit"}
                          >
                            <Save className="h-4 w-4 mr-2" />
                            {loading === "edit" ? "Guardando..." : "Guardar"}
                          </Button>
                        </DialogFooter>
                      </DialogContent>
                    </Dialog>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <Database className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No hay configuraciones. Ejecuta el seed primero.</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Transacciones Recientes */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            Transacciones Activas Recientes
          </CardTitle>
        </CardHeader>
        <CardContent>
          {allTransactions && allTransactions.length > 0 ? (
            <div className="space-y-3">
              {allTransactions.slice(0, 8).map((transaction) => (
                <div
                  key={transaction._id}
                  className="flex items-center justify-between p-4 bg-gray-50 rounded-lg border"
                >
                  <div className="flex items-center gap-4">
                    {/* Avatar del Usuario */}
                    <div className="flex items-center gap-3">
                      {transaction.user?.avatar ? (
                        <img 
                          src={transaction.user.avatar} 
                          alt={transaction.user.name || "Usuario"}
                          className="w-10 h-10 rounded-full border-2 border-gray-200"
                        />
                      ) : (
                        <div className="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                          <User className="h-5 w-5 text-gray-600" />
                        </div>
                      )}
                      <div>
                        <p className="font-medium text-sm">
                          {transaction.user?.name || "Usuario desconocido"}
                        </p>
                        <p className="text-xs text-gray-500">
                          {transaction.user?.email || "Sin email"}
                        </p>
                      </div>
                    </div>
                    
                    {/* Información de la transacción */}
                    <div className="flex items-center gap-3">
                      {getActionIcon(transaction.action)}
                      <div>
                        <p className="font-medium">{transaction.description}</p>
                        <div className="flex items-center gap-4 text-sm text-gray-600">
                          <span>
                            {new Date(transaction.createdAt).toLocaleDateString('es-ES', {
                              day: '2-digit',
                              month: '2-digit',
                              year: 'numeric',
                              hour: '2-digit',
                              minute: '2-digit'
                            })}
                          </span>
                          {transaction.expiresAt && (
                            <span className="flex items-center gap-1">
                              <Clock className="h-3 w-3" />
                              Expira: {new Date(transaction.expiresAt).toLocaleDateString('es-ES')}
                            </span>
                          )}
                          {transaction.property && (
                            <span className="flex items-center gap-1">
                              <Building2 className="h-3 w-3" />
                              {transaction.property.title.slice(0, 30)}...
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <Badge className="bg-red-100 text-red-800">
                      -{transaction.creditsCost} créditos
                    </Badge>
                    {getStatusBadge(transaction.status)}
                    <Badge variant="outline" className="text-xs">
                      {transaction.user?.role || "buyer"}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No hay transacciones registradas.</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Propiedad Premium Actual */}
      {premiumProperty && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Crown className="h-5 w-5 text-purple-600" />
              Propiedad Premium Actual
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-4 p-4 bg-purple-50 rounded-lg">
              <div className="w-16 h-16 bg-purple-100 rounded-lg flex items-center justify-center">
                <Crown className="h-8 w-8 text-purple-600" />
              </div>
              <div className="flex-1">
                <h3 className="font-semibold text-purple-900">{premiumProperty.title}</h3>
                <p className="text-purple-700">{premiumProperty.city}</p>
                <p className="text-sm text-purple-600">
                  Expira: {premiumProperty.premiumHomeUntil ? 
                    new Date(premiumProperty.premiumHomeUntil).toLocaleDateString() : 
                    'Sin fecha'
                  }
                </p>
              </div>
              <Badge className="bg-purple-600 text-white">
                VIP ACTIVO
              </Badge>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Gestión de Planes de Usuario */}
      {allUsers && (
        <UserPlanManager
          users={allUsers}
          onRefresh={() => window.location.reload()}
        />
      )}

      {/* Debug Info */}
      <Card>
        <CardHeader>
          <CardTitle>Debug Info</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <p><strong>Usuario ID:</strong> {user?.id}</p>
          <p><strong>Email:</strong> {currentUser?.email}</p>
          <p><strong>Rol actual:</strong> {currentUser?.role || "Sin rol"}</p>
          <Button
            onClick={handleMakeAdmin}
            variant="outline"
            className="mt-2"
          >
            Hacer Admin a Este Usuario
          </Button>
        </CardContent>
      </Card>
    </div>
  );
} 