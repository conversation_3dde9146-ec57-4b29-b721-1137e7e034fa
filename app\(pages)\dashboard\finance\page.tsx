"use client"
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import { CreditsDisplay } from "@/components/ui/credits-display";
import { api } from "@/convex/_generated/api";
import { useUser } from "@clerk/clerk-react";
import { useQuery } from "convex/react";
import {
  CreditCard,
  Crown,
  Calendar,
  Zap,
  Building2,
  CheckCircle,
  XCircle,
  AlertTriangle
} from "lucide-react";
import { useState, useEffect } from "react";
import { toast } from "sonner";
import { STRIPE_CONFIG, getPlanInfo } from "@/lib/stripe-config";
import { DowngradeModal } from "@/components/downgrade-modal";

export default function SubscriptionPage() {
  const { user } = useUser();
  const [loading, setLoading] = useState(false);
  const [loadingPlan, setLoadingPlan] = useState<"premium" | null>(null);
  const [showDowngradeModal, setShowDowngradeModal] = useState(false);

  const subscription = useQuery(api.subscriptions.getUserSubscription);
  const usageStats = useQuery(api.subscriptions.getUsageStats);

  const handleUpgrade = async (plan: "premium") => {
    setLoadingPlan(plan);
    
    try {
      // Guardar el plan en localStorage para identificarlo después
      localStorage.setItem('checkoutPlan', plan);
      
      const response = await fetch("/api/stripe/checkout", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ plan }),
      });

      const data = await response.json();

      if (data.checkoutUrl) {
        // Redirigir a Stripe
        window.location.href = data.checkoutUrl;
      } else {
        toast.error("Error al crear el checkout");
      }
    } catch (error) {
      console.error("Error:", error);
      toast.error("Error al procesar el pago");
    } finally {
      setLoadingPlan(null);
    }
  };

  // Usar la configuración centralizada
  const freePlan = getPlanInfo("free");
  const premiumPlan = getPlanInfo("premium");

  useEffect(() => {
    // Detectar si venimos del éxito del checkout
    const urlParams = new URLSearchParams(window.location.search);
    const success = urlParams.get('success');
    const sessionId = urlParams.get('session_id');

    if (success === 'true' && sessionId) {
      console.log("🎯 Procesando éxito del checkout...");
      
      // Obtener el plan desde localStorage
      const checkoutPlan = localStorage.getItem('checkoutPlan') || 'premium';
      
      // TODO: Procesar la suscripción con Stripe
      // Por ahora activamos directamente desde el frontend
      toast.success("¡Suscripción activada correctamente!");
      
      // Limpiar localStorage y URL
      localStorage.removeItem('checkoutPlan');
      const newUrl = window.location.pathname;
      window.history.replaceState({}, '', newUrl);
      
      // Refrescar para mostrar los nuevos datos
      setTimeout(() => window.location.reload(), 1000);
    }
  }, []);

  if (!user) {
    return (
      <div className="flex items-center justify-center py-20">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!subscription) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-3xl font-bold tracking-tight">Suscripción</h2>
        </div>
        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="mt-2 text-muted-foreground">Cargando información de suscripción...</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  const currentLimits = getPlanInfo(subscription.plan as "free" | "premium");
  const isPremiumPlan = subscription.plan === "premium";

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800"><CheckCircle className="h-3 w-3 mr-1" />Activa</Badge>;
      case 'inactive':
        return <Badge className="bg-red-100 text-red-800"><XCircle className="h-3 w-3 mr-1" />Inactiva</Badge>;
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800"><Calendar className="h-3 w-3 mr-1" />Pendiente</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getPlanBadge = (plan: string) => {
    switch (plan) {
      case 'free':
        return <Badge variant="outline">Gratuito</Badge>;
      case 'premium':
        return <Badge className="bg-purple-100 text-purple-800"><Crown className="h-3 w-3 mr-1" />Premium</Badge>;
      default:
        return <Badge variant="outline">{plan}</Badge>;
    }
  };

  return (
    <div className="flex flex-col gap-6 p-4 md:p-6">
      {/* Indicador de Modo Sandbox */}
      {process.env.NODE_ENV === "development" && (
        <Card className="border-yellow-200 bg-yellow-50">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="w-3 h-3 bg-yellow-500 rounded-full animate-pulse"></div>
              <div>
                <h3 className="font-medium text-yellow-800">
                  🧪 Modo Testing - Stripe Sandbox
                </h3>
                <p className="text-sm text-yellow-700">
                  Las compras son reales pero NO se cobrará dinero. Usa tarjetas de prueba de Stripe.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      <div>
        <h1 className="text-3xl font-semibold tracking-tight">Suscripción</h1>
        <p className="text-muted-foreground mt-2">
          Gestiona tu plan de suscripción y revisa los detalles de tu cuenta
        </p>
      </div>

      {/* Resumen de Créditos */}
      <CreditsDisplay variant="card" />

      {/* Subscription Details */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Main Subscription Card */}
        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="h-5 w-5" />
              Detalles de Suscripción
            </CardTitle>
          </CardHeader>
          <CardContent>
            {!subscription ? (
              <div className="space-y-4">
                <Skeleton className="h-4 w-[180px]" />
                <Skeleton className="h-4 w-[150px]" />
                <Skeleton className="h-4 w-[200px]" />
                <Skeleton className="h-4 w-[170px]" />
              </div>
            ) : (
              <div className="grid gap-6 md:grid-cols-2">
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-muted-foreground">Estado:</span>
                    {getStatusBadge(subscription.status)}
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-muted-foreground">Plan:</span>
                    {getPlanBadge(subscription.plan)}
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-muted-foreground">Premium:</span>
                    <span className="font-medium">{subscription.isPremium ? 'Sí' : 'No'}</span>
                  </div>
                </div>
                
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-muted-foreground">Créditos:</span>
                    <span className="font-medium text-lg">{subscription.credits}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-muted-foreground">Propiedades Máximas:</span>
                    <span className="font-medium text-lg">{subscription.maxProperties}</span>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Usage Stats */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building2 className="h-5 w-5" />
              Uso Actual
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-muted-foreground">Propiedades Publicadas:</span>
                <span className="font-medium">
                  {subscription.propertiesCount} / {subscription.maxProperties}
                </span>
              </div>
              
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ 
                    width: `${Math.min((subscription.propertiesCount / subscription.maxProperties) * 100, 100)}%` 
                  }}
                ></div>
              </div>

              <div className="flex justify-between items-center">
                <span className="text-muted-foreground">Créditos Utilizados:</span>
                <span className="font-medium">
                  {subscription.creditsUsed} / {subscription.credits}
                </span>
              </div>
              
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-green-600 h-2 rounded-full transition-all duration-300"
                  style={{ 
                    width: `${Math.min((subscription.creditsUsed / subscription.credits) * 100, 100)}%` 
                  }}
                ></div>
              </div>

              {subscription.creditsUsed >= subscription.credits && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <div className="flex items-center gap-2 text-red-800">
                    <AlertTriangle className="h-4 w-4" />
                    <span className="font-medium">Créditos Agotados</span>
                  </div>
                  <p className="text-red-700 text-sm mt-1">
                    Has usado todos tus créditos para este mes. 
                    {subscription.plan !== "premium" && " Considera hacer upgrade para obtener más créditos."}
                  </p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Plan Actions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Crown className="h-5 w-5" />
              Gestionar Plan
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {subscription?.plan === 'free' ? (
              <>
                <p className="text-sm text-muted-foreground">
                  Actualiza a Premium para obtener funciones avanzadas y límites ilimitados.
                </p>
                <Button
                  className="w-full bg-purple-600 hover:bg-purple-700"
                  onClick={() => handleUpgrade("premium")}
                  disabled={loadingPlan === "premium"}
                >
                  <Crown className="h-4 w-4 mr-2" />
                  {loadingPlan === "premium" ? "Procesando..." : "Actualizar a Premium"}
                </Button>
              </>
            ) : (
              <>
                <p className="text-sm text-muted-foreground">
                  Gestiona tu suscripción premium y métodos de pago.
                </p>
                <div className="space-y-2">
                  <Button
                    variant="outline"
                    className="w-full"
                    disabled
                  >
                    <Crown className="h-4 w-4 mr-2" />
                    Plan Premium Activo
                  </Button>
                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={() => setShowDowngradeModal(true)}
                  >
                    Cambiar a Plan Inferior
                  </Button>
                  <Button variant="outline" className="w-full">
                    Gestionar Facturación
                  </Button>
                </div>
              </>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Plan Comparison - Solo Free vs Premium */}
      {!isPremiumPlan && (
        <div className="grid gap-6 lg:grid-cols-2">
          {/* Free Plan */}
          <Card className={subscription.plan === "free" ? "ring-2 ring-blue-500" : ""}>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                {freePlan.name}
                {subscription.plan === "free" && (
                  <Badge variant="default">Actual</Badge>
                )}
              </CardTitle>
              <div className="text-2xl font-bold">{freePlan.priceDisplay}</div>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2 text-sm">
                {freePlan.features.map((feature, index) => (
                  <li key={index} className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    {feature}
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>

          {/* Premium Plan */}
          <Card className="ring-2 ring-purple-500">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                Premium
                <Badge variant="secondary" className="bg-purple-100 text-purple-800">Recomendado</Badge>
              </CardTitle>
              <div className="text-2xl font-bold">{premiumPlan.priceDisplay}</div>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2 text-sm mb-4">
                {premiumPlan.features.map((feature, index) => (
                  <li key={index} className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    {feature}
                  </li>
                ))}
              </ul>
              <Button
                className="w-full bg-purple-600 hover:bg-purple-700"
                onClick={() => handleUpgrade("premium")}
                disabled={loadingPlan === "premium"}
              >
                {loadingPlan === "premium" ? "Procesando..." : "Actualizar a Premium"}
              </Button>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Modal de Downgrade */}
      {subscription?.plan === 'premium' && (
        <DowngradeModal
          isOpen={showDowngradeModal}
          onClose={() => setShowDowngradeModal(false)}
          currentPlan="premium"
          onSuccess={() => {
            // Refrescar datos después del downgrade
            window.location.reload();
          }}
        />
      )}
    </div>
  );
}