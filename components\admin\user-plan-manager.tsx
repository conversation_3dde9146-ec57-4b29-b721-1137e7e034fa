"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import { useMutation, useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Crown, Zap, User, RefreshCw, Bell } from "lucide-react";

interface User {
  _id: string;
  tokenIdentifier: string; // Necesario para cambiar plan
  name?: string;
  email: string;
  role?: string;
  trialEndsAt?: number | null;
  subscriptionStatus?: string;
  isTrialActive?: boolean;
  plan?: string;
  createdAt: number;
}

interface UserPlanManagerProps {
  users: User[];
  onRefresh?: () => void;
}

export function UserPlanManager({ users, onRefresh }: UserPlanManagerProps) {
  const [selectedUser, setSelectedUser] = useState<string>("");
  const [newPlan, setNewPlan] = useState<"free" | "premium">("free");
  const [resetCredits, setResetCredits] = useState(false);
  const [loading, setLoading] = useState(false);

  const adminChangePlan = useMutation(api.admin.adminChangePlan);

  const handleChangePlan = async () => {
    if (!selectedUser) {
      toast.error("Selecciona un usuario");
      return;
    }

    try {
      setLoading(true);
      
      const result = await adminChangePlan({
        userId: selectedUser,
        newPlan,
        resetCredits,
      });

      if (result.success) {
        toast.success(result.message);
        onRefresh?.();
        setSelectedUser("");
        setNewPlan("free");
        setResetCredits(false);
      }
    } catch (error: any) {
      console.error("Error cambiando plan:", error);
      toast.error(error.message || "Error al cambiar el plan");
    } finally {
      setLoading(false);
    }
  };

  const getPlanBadge = (plan?: string) => {
    switch (plan) {
      case 'premium':
        return <Badge className="bg-purple-100 text-purple-800"><Crown className="h-3 w-3 mr-1" />Premium</Badge>;
      default:
        return <Badge variant="outline">Gratuito</Badge>;
    }
  };

  const getStatusBadge = (status?: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800">Activo</Badge>;
      case 'trialing':
        return <Badge className="bg-blue-100 text-blue-800">Trial</Badge>;
      default:
        return <Badge variant="outline">{status || 'Free'}</Badge>;
    }
  };

  // Filtrar solo usuarios que necesitan suscripción (sellers y agents)
  const eligibleUsers = users.filter(user => 
    user.role === 'seller' || user.role === 'agent' || user.role === 'admin'
  );

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Cambiar Plan de Usuario (Para Pruebas)
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="user-select">Usuario</Label>
              <Select value={selectedUser} onValueChange={setSelectedUser}>
                <SelectTrigger>
                  <SelectValue placeholder="Selecciona un usuario" />
                </SelectTrigger>
                <SelectContent>
                  {eligibleUsers.map((user) => (
                    <SelectItem key={user._id} value={user.tokenIdentifier}>
                      <div className="flex items-center gap-2">
                        <span>{user.name || user.email}</span>
                        <span className="text-xs text-muted-foreground">({user.role})</span>
                        {getPlanBadge(user.plan)}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="plan-select">Nuevo Plan</Label>
              <Select value={newPlan} onValueChange={(value: "free" | "premium") => setNewPlan(value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="free">
                    <div className="flex items-center gap-2">
                      <span>Gratuito</span>
                      <span className="text-xs text-muted-foreground">(10 créditos, 5 propiedades)</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="premium">
                    <div className="flex items-center gap-2">
                      <Crown className="h-3 w-3" />
                      <span>Premium</span>
                      <span className="text-xs text-muted-foreground">(300 créditos, ilimitadas)</span>
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              id="reset-credits"
              checked={resetCredits}
              onCheckedChange={setResetCredits}
            />
            <Label htmlFor="reset-credits">Resetear créditos usados a 0</Label>
          </div>

          <Button 
            onClick={handleChangePlan}
            disabled={loading || !selectedUser}
            className="w-full"
          >
            {loading ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                Cambiando Plan...
              </>
            ) : (
              `Cambiar a ${newPlan}`
            )}
          </Button>
        </CardContent>
      </Card>

      {/* Lista de usuarios con sus planes actuales */}
      <Card>
        <CardHeader>
          <CardTitle>Usuarios con Suscripción</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {eligibleUsers.length === 0 ? (
              <p className="text-muted-foreground text-center py-4">
                No hay usuarios con roles que requieran suscripción
              </p>
            ) : (
              eligibleUsers.map((user) => (
                <div key={user._id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <div>
                      <p className="font-medium">{user.name || "Sin nombre"}</p>
                      <p className="text-sm text-muted-foreground">{user.email}</p>
                    </div>
                    <Badge variant="outline">{user.role}</Badge>
                  </div>
                  <div className="flex items-center gap-2">
                    {getPlanBadge(user.plan)}
                    {getStatusBadge(user.subscriptionStatus)}
                    {user.isTrialActive && (
                      <Badge className="bg-orange-100 text-orange-800">Trial Activo</Badge>
                    )}
                  </div>
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>

      {/* Componente de prueba para notificaciones de agenda */}
      <AgendaNotificationTester />
    </div>
  );
}

// Componente para probar notificaciones de agenda
function AgendaNotificationTester() {
  const [loading, setLoading] = useState(false);
  const createTestRequest = useMutation(api.appointments.createTestAppointmentRequest);
  const eligibleUsers = useQuery(api.admin.getAllUsers, {});

  const handleCreateTestNotification = async (hostId: string) => {
    setLoading(true);
    try {
      await createTestRequest({
        hostId,
        title: "Solicitud de Visita - Prueba de Notificación"
      });
      toast.success("Solicitud de prueba creada exitosamente");
    } catch (error) {
      console.error("Error:", error);
      toast.error("Error al crear solicitud de prueba");
    } finally {
      setLoading(false);
    }
  };

  const agentUsers = eligibleUsers?.filter(user =>
    user.role === 'dueño' || user.role === 'agente'
  ) || [];

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Bell className="h-5 w-5" />
          Prueba de Notificaciones de Agenda
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <p className="text-sm text-muted-foreground">
          Crea solicitudes de cita de prueba para verificar que las notificaciones de agenda funcionan correctamente.
        </p>

        <div className="space-y-3">
          {agentUsers.map((user) => (
            <div key={user._id} className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <p className="font-medium">{user.name || "Sin nombre"}</p>
                <p className="text-sm text-muted-foreground">{user.email}</p>
              </div>
              <Button
                onClick={() => handleCreateTestNotification(user.tokenIdentifier)}
                disabled={loading}
                size="sm"
                variant="outline"
              >
                {loading ? (
                  <RefreshCw className="h-4 w-4 animate-spin" />
                ) : (
                  "Crear Solicitud"
                )}
              </Button>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
