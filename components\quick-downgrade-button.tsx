"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowDown } from "lucide-react";
import { DowngradeModal } from "./downgrade-modal";

interface QuickDowngradeButtonProps {
  currentPlan: "premium"; // Solo premium puede hacer downgrade
  className?: string;
}

export function QuickDowngradeButton({ currentPlan, className }: QuickDowngradeButtonProps) {
  const [showModal, setShowModal] = useState(false);

  return (
    <>
      <Button
        variant="outline"
        size="sm"
        onClick={() => setShowModal(true)}
        className={className}
      >
        <ArrowDown className="h-4 w-4 mr-2" />
        Cambiar Plan
      </Button>

      <DowngradeModal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        currentPlan={currentPlan}
        onSuccess={() => {
          window.location.reload();
        }}
      />
    </>
  );
}
