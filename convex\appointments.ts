import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { Id } from "./_generated/dataModel";

// === QUERIES ===

// Obtener agenda del usuario (vista principal)
export const getUserAppointments = query({
  args: { 
    userId: v.string(),
    startDate: v.optional(v.string()), // Filtro por fecha
    endDate: v.optional(v.string()),
    status: v.optional(v.string()) // Filtro por status
  },
  handler: async (ctx, args) => {
    let appointmentsQuery = ctx.db
      .query("appointments")
      .withIndex("by_host", (q) => q.eq("hostId", args.userId));

    if (args.startDate) {
      appointmentsQuery = appointmentsQuery.filter((q) => 
        q.gte(q.field("startTime"), args.startDate!)
      );
    }

    if (args.endDate) {
      appointmentsQuery = appointmentsQuery.filter((q) => 
        q.lte(q.field("startTime"), args.endDate!)
      );
    }

    if (args.status) {
      appointmentsQuery = appointmentsQuery.filter((q) => 
        q.eq(q.field("status"), args.status)
      );
    }

    const appointments = await appointmentsQuery
      .order("desc")
      .collect();

    // Enriquecer con datos de propiedad si existe
    const enrichedAppointments = await Promise.all(
      appointments.map(async (appointment) => {
        let property = null;
        if (appointment.propertyId) {
          property = await ctx.db.get(appointment.propertyId);
        }
        return {
          ...appointment,
          property
        };
      })
    );

    return enrichedAppointments;
  },
});

// Obtener citas como invitado
export const getGuestAppointments = query({
  args: { userId: v.string() },
  handler: async (ctx, args) => {
    const appointments = await ctx.db
      .query("appointments")
      .withIndex("by_guest", (q) => q.eq("guestId", args.userId))
      .order("desc")
      .collect();

    return appointments;
  },
});

// Obtener disponibilidad del usuario
export const getUserAvailability = query({
  args: { userId: v.string() },
  handler: async (ctx, args) => {
    const availability = await ctx.db
      .query("availability")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .collect();

    return availability;
  },
});

// Obtener solicitudes de cita pendientes
export const getPendingRequests = query({
  args: { hostId: v.string() },
  handler: async (ctx, args) => {
    const requests = await ctx.db
      .query("appointmentRequests")
      .withIndex("by_host", (q) => q.eq("hostId", args.hostId))
      .filter((q) => q.eq(q.field("status"), "pending"))
      .order("desc")
      .collect();

    // Enriquecer con datos de propiedad
    const enrichedRequests = await Promise.all(
      requests.map(async (request) => {
        let property = null;
        if (request.propertyId) {
          property = await ctx.db.get(request.propertyId);
        }
        return {
          ...request,
          property
        };
      })
    );

    return enrichedRequests;
  },
});

// Verificar slots disponibles para una fecha
export const getAvailableSlots = query({
  args: { 
    userId: v.string(), 
    date: v.string(), // formato YYYY-MM-DD
    duration: v.optional(v.number()) // duración en minutos, default 60
  },
  handler: async (ctx, args) => {
    const targetDate = new Date(args.date);
    const dayOfWeek = targetDate.getDay() as 0 | 1 | 2 | 3 | 4 | 5 | 6;
    const duration = args.duration || 60;

    // Obtener disponibilidad para ese día
    const availability = await ctx.db
      .query("availability")
      .withIndex("by_user_day", (q) => 
        q.eq("userId", args.userId).eq("dayOfWeek", dayOfWeek)
      )
      .filter((q) => q.eq(q.field("isEnabled"), true))
      .first();

    if (!availability) {
      return []; // No disponible ese día
    }

    // Obtener citas existentes para esa fecha
    const startOfDay = new Date(targetDate);
    startOfDay.setHours(0, 0, 0, 0);
    const endOfDay = new Date(targetDate);
    endOfDay.setHours(23, 59, 59, 999);

    const existingAppointments = await ctx.db
      .query("appointments")
      .withIndex("by_host_date", (q) => q.eq("hostId", args.userId))
      .filter((q) => 
        q.and(
          q.gte(q.field("startTime"), startOfDay.toISOString()),
          q.lte(q.field("startTime"), endOfDay.toISOString()),
          q.neq(q.field("status"), "cancelled")
        )
      )
      .collect();

    // Calcular slots disponibles
    const slots = [];
    const [startHour, startMinute] = availability.startTime.split(':').map(Number);
    const [endHour, endMinute] = availability.endTime.split(':').map(Number);
    
    const slotDuration = availability.slotDuration || duration;
    const breakTime = availability.breakTime || 0;

    // Crear fechas en hora local de Guatemala (UTC-6)
    // Usamos UTC y ajustamos manualmente para evitar problemas de zona horaria
    let currentTime = new Date(Date.UTC(targetDate.getFullYear(), targetDate.getMonth(), targetDate.getDate(), startHour + 6, startMinute, 0, 0));
    const endTime = new Date(Date.UTC(targetDate.getFullYear(), targetDate.getMonth(), targetDate.getDate(), endHour + 6, endMinute, 0, 0));

    while (currentTime < endTime) {
      const slotEnd = new Date(currentTime.getTime() + slotDuration * 60000);
      
      if (slotEnd <= endTime) {
        // Verificar si hay conflicto con citas existentes
        const hasConflict = existingAppointments.some(appointment => {
          const appointmentStart = new Date(appointment.startTime);
          const appointmentEnd = new Date(appointment.endTime);
          
          return (currentTime < appointmentEnd && slotEnd > appointmentStart);
        });

        if (!hasConflict) {
          slots.push({
            startTime: currentTime.toISOString(),
            endTime: slotEnd.toISOString(),
            duration: slotDuration
          });
        }
      }

      currentTime = new Date(currentTime.getTime() + (slotDuration + breakTime) * 60000);
    }

    return slots;
  },
});

// Obtener fechas disponibles para un usuario (próximos días con slots)
export const getAvailableDates = query({
  args: { 
    userId: v.string(),
    days: v.optional(v.number()), // días hacia adelante a verificar, default 14
    duration: v.optional(v.number()) // duración de cita en minutos, default 60
  },
  handler: async (ctx, args) => {
    const daysToCheck = args.days || 14;
    const duration = args.duration || 60;
    const availableDates = [];

    // Obtener la configuración de disponibilidad del usuario
    const userAvailability = await ctx.db
      .query("availability")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .filter((q) => q.eq(q.field("isEnabled"), true))
      .collect();

    if (userAvailability.length === 0) {
      return []; // Usuario no tiene disponibilidad configurada
    }

    // Verificar cada día hacia adelante
    for (let i = 1; i <= daysToCheck; i++) {
      const targetDate = new Date();
      targetDate.setDate(targetDate.getDate() + i);
      targetDate.setHours(0, 0, 0, 0);
      
      const dayOfWeek = targetDate.getDay() as 0 | 1 | 2 | 3 | 4 | 5 | 6;
      
      // Verificar si el usuario está disponible este día
      const dayAvailability = userAvailability.find(a => a.dayOfWeek === dayOfWeek);
      if (!dayAvailability) continue;

      // Obtener citas existentes para esta fecha
      const startOfDay = new Date(targetDate);
      const endOfDay = new Date(targetDate);
      endOfDay.setHours(23, 59, 59, 999);

      const existingAppointments = await ctx.db
        .query("appointments")
        .withIndex("by_host_date", (q) => q.eq("hostId", args.userId))
        .filter((q) => 
          q.and(
            q.gte(q.field("startTime"), startOfDay.toISOString()),
            q.lte(q.field("startTime"), endOfDay.toISOString()),
            q.neq(q.field("status"), "cancelled")
          )
        )
        .collect();

      // Calcular si hay al menos un slot disponible
      const [startHour, startMinute] = dayAvailability.startTime.split(':').map(Number);
      const [endHour, endMinute] = dayAvailability.endTime.split(':').map(Number);
      
      const slotDuration = dayAvailability.slotDuration || duration;
      const breakTime = dayAvailability.breakTime || 0;

      // Crear fechas en hora local de Guatemala (UTC-6)
      // Usamos UTC y ajustamos manualmente para evitar problemas de zona horaria
      let currentTime = new Date(Date.UTC(targetDate.getFullYear(), targetDate.getMonth(), targetDate.getDate(), startHour + 6, startMinute, 0, 0));
      const endTime = new Date(Date.UTC(targetDate.getFullYear(), targetDate.getMonth(), targetDate.getDate(), endHour + 6, endMinute, 0, 0));

      let hasAvailableSlot = false;

      while (currentTime < endTime && !hasAvailableSlot) {
        const slotEnd = new Date(currentTime.getTime() + slotDuration * 60000);
        
        if (slotEnd <= endTime) {
          // Verificar si hay conflicto con citas existentes
          const hasConflict = existingAppointments.some(appointment => {
            const appointmentStart = new Date(appointment.startTime);
            const appointmentEnd = new Date(appointment.endTime);
            
            return (currentTime < appointmentEnd && slotEnd > appointmentStart);
          });

          if (!hasConflict) {
            hasAvailableSlot = true;
          }
        }

        currentTime = new Date(currentTime.getTime() + (slotDuration + breakTime) * 60000);
      }

      // Si hay al menos un slot disponible, agregar la fecha
      if (hasAvailableSlot) {
        availableDates.push({
          value: targetDate.toISOString().split('T')[0], // YYYY-MM-DD
          label: targetDate.toLocaleDateString('es-ES', { 
            weekday: 'long', 
            day: 'numeric', 
            month: 'long' 
          }),
          date: targetDate.toISOString(),
          dayOfWeek: dayOfWeek,
          availableFrom: dayAvailability.startTime,
          availableTo: dayAvailability.endTime
        });
      }
    }

    return availableDates;
  },
});

// === MUTATIONS ===

// Crear nueva cita
export const createAppointment = mutation({
  args: {
    title: v.string(),
    description: v.optional(v.string()),
    hostId: v.string(),
    guestId: v.string(),
    guestName: v.string(),
    guestEmail: v.string(),
    guestPhone: v.optional(v.string()),
    startTime: v.string(),
    endTime: v.string(),
    timezone: v.string(),
    type: v.union(
      v.literal("property_viewing"),
      v.literal("consultation"),
      v.literal("negotiation"),
      v.literal("document_signing"),
      v.literal("other")
    ),
    meetingType: v.union(
      v.literal("in_person"),
      v.literal("video_call"),
      v.literal("phone_call")
    ),
    location: v.optional(v.string()),
    meetingUrl: v.optional(v.string()),
    propertyId: v.optional(v.id("properties")),
    notes: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const appointmentId = await ctx.db.insert("appointments", {
      ...args,
      status: "scheduled",
      remindersSent: false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    });

    return appointmentId;
  },
});

// Actualizar cita
export const updateAppointment = mutation({
  args: {
    appointmentId: v.id("appointments"),
    title: v.optional(v.string()),
    description: v.optional(v.string()),
    startTime: v.optional(v.string()),
    endTime: v.optional(v.string()),
    status: v.optional(v.union(
      v.literal("scheduled"),
      v.literal("confirmed"),
      v.literal("completed"),
      v.literal("cancelled"),
      v.literal("no_show")
    )),
    location: v.optional(v.string()),
    meetingUrl: v.optional(v.string()),
    notes: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const { appointmentId, ...updates } = args;
    
    await ctx.db.patch(appointmentId, {
      ...updates,
      updatedAt: new Date().toISOString(),
    });

    return appointmentId;
  },
});

// Cancelar cita
export const cancelAppointment = mutation({
  args: {
    appointmentId: v.id("appointments"),
    cancelledBy: v.string(),
    cancellationReason: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.appointmentId, {
      status: "cancelled",
      cancelledAt: new Date().toISOString(),
      cancelledBy: args.cancelledBy,
      cancellationReason: args.cancellationReason,
      updatedAt: new Date().toISOString(),
    });

    return args.appointmentId;
  },
});

// Configurar disponibilidad
export const setAvailability = mutation({
  args: {
    userId: v.string(),
    dayOfWeek: v.union(v.literal(0), v.literal(1), v.literal(2), v.literal(3), v.literal(4), v.literal(5), v.literal(6)),
    startTime: v.string(),
    endTime: v.string(),
    isEnabled: v.boolean(),
    timezone: v.string(),
    slotDuration: v.optional(v.number()),
    breakTime: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    // Verificar si ya existe configuración para este día
    const existing = await ctx.db
      .query("availability")
      .withIndex("by_user_day", (q) => 
        q.eq("userId", args.userId).eq("dayOfWeek", args.dayOfWeek)
      )
      .first();

    if (existing) {
      // Actualizar existente
      await ctx.db.patch(existing._id, {
        startTime: args.startTime,
        endTime: args.endTime,
        isEnabled: args.isEnabled,
        timezone: args.timezone,
        slotDuration: args.slotDuration,
        breakTime: args.breakTime,
        updatedAt: new Date().toISOString(),
      });
      return existing._id;
    } else {
      // Crear nuevo
      const availabilityId = await ctx.db.insert("availability", {
        ...args,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      });
      return availabilityId;
    }
  },
});

// Solicitar cita
export const requestAppointment = mutation({
  args: {
    hostId: v.string(),
    guestId: v.string(),
    guestName: v.string(),
    guestEmail: v.string(),
    guestPhone: v.optional(v.string()),
    propertyId: v.optional(v.id("properties")),
    requestedStartTime: v.string(),
    requestedEndTime: v.string(),
    message: v.optional(v.string()),
    type: v.union(
      v.literal("property_viewing"),
      v.literal("consultation"),
      v.literal("negotiation"),
      v.literal("document_signing"),
      v.literal("other")
    ),
    meetingType: v.union(
      v.literal("in_person"),
      v.literal("video_call"),
      v.literal("phone_call")
    ),
  },
  handler: async (ctx, args) => {
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7); // Expira en 7 días

    const requestId = await ctx.db.insert("appointmentRequests", {
      ...args,
      status: "pending",
      createdAt: new Date().toISOString(),
      expiresAt: expiresAt.toISOString(),
    });

    return requestId;
  },
});

// Obtener estadísticas de notificaciones para la agenda
export const getAgendaNotificationStats = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return null;
    }

    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    // Solicitudes pendientes de respuesta
    const pendingRequests = await ctx.db
      .query("appointmentRequests")
      .withIndex("by_host", (q) => q.eq("hostId", identity.subject))
      .filter((q) => q.eq(q.field("status"), "pending"))
      .collect();

    // Citas próximas (hoy y mañana) que requieren atención
    const upcomingAppointments = await ctx.db
      .query("appointments")
      .withIndex("by_host", (q) => q.eq("hostId", identity.subject))
      .filter((q) =>
        q.and(
          q.eq(q.field("status"), "confirmed"),
          q.gte(q.field("startTime"), today.toISOString()),
          q.lt(q.field("startTime"), tomorrow.toISOString())
        )
      )
      .collect();

    // Citas que empiezan en las próximas 2 horas
    const soonAppointments = await ctx.db
      .query("appointments")
      .withIndex("by_host", (q) => q.eq("hostId", identity.subject))
      .filter((q) => {
        const twoHoursFromNow = new Date(now.getTime() + 2 * 60 * 60 * 1000);
        return q.and(
          q.eq(q.field("status"), "confirmed"),
          q.gte(q.field("startTime"), now.toISOString()),
          q.lte(q.field("startTime"), twoHoursFromNow.toISOString())
        );
      })
      .collect();

    // Calcular total de notificaciones
    const totalNotifications = pendingRequests.length + soonAppointments.length;

    return {
      pendingRequests: pendingRequests.length,
      upcomingToday: upcomingAppointments.length,
      startingSoon: soonAppointments.length,
      totalNotifications,
      hasNotifications: totalNotifications > 0
    };
  },
});

// Función de prueba para crear solicitudes de cita (solo para testing)
export const createTestAppointmentRequest = mutation({
  args: {
    hostId: v.string(),
    title: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("No autenticado");
    }

    // Solo admin puede crear solicitudes de prueba
    const user = await ctx.db
      .query("users")
      .withIndex("by_token", (q) => q.eq("tokenIdentifier", identity.subject))
      .first();

    if (!user || user.role !== "admin") {
      throw new Error("Solo administradores pueden crear solicitudes de prueba");
    }

    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7);

    const now = new Date();
    const endTime = new Date(now.getTime() + 60 * 60 * 1000); // 1 hora después

    const requestId = await ctx.db.insert("appointmentRequests", {
      hostId: args.hostId,
      guestId: "test_guest_" + Date.now(),
      guestName: "Cliente de Prueba",
      guestEmail: "<EMAIL>",
      guestPhone: "+1234567890",
      requestedStartTime: now.toISOString(),
      requestedEndTime: endTime.toISOString(),
      message: args.title || "Solicitud de visita de prueba para verificar notificaciones",
      type: "property_viewing",
      meetingType: "in_person",
      status: "pending",
      createdAt: new Date().toISOString(),
      expiresAt: expiresAt.toISOString(),
    });

    return requestId;
  },
});

// Responder solicitud de cita
export const respondToRequest = mutation({
  args: {
    requestId: v.id("appointmentRequests"),
    approved: v.boolean(),
    response: v.optional(v.string()),
    // Si se aprueba, datos para crear la cita
    finalStartTime: v.optional(v.string()),
    finalEndTime: v.optional(v.string()),
    location: v.optional(v.string()),
    meetingUrl: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const request = await ctx.db.get(args.requestId);
    if (!request) throw new Error("Solicitud no encontrada");

    const status = args.approved ? "approved" : "rejected";

    await ctx.db.patch(args.requestId, {
      status,
      response: args.response,
      respondedAt: new Date().toISOString(),
    });

    // Si se aprueba, crear la cita
    if (args.approved) {
      const appointmentId = await ctx.db.insert("appointments", {
        title: `${request.type} - ${request.guestName}`,
        hostId: request.hostId,
        guestId: request.guestId,
        guestName: request.guestName,
        guestEmail: request.guestEmail,
        guestPhone: request.guestPhone,
        startTime: args.finalStartTime || request.requestedStartTime,
        endTime: args.finalEndTime || request.requestedEndTime,
        timezone: "America/Guatemala", // Zona horaria para Guatemala
        type: request.type,
        meetingType: request.meetingType,
        location: args.location,
        meetingUrl: args.meetingUrl,
        propertyId: request.propertyId,
        status: "confirmed",
        remindersSent: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      });

      // Actualizar la solicitud con la cita creada
      await ctx.db.patch(args.requestId, {
        appointmentId,
      });

      return appointmentId;
    }

    return args.requestId;
  },
});

// Obtener estadísticas de agenda
export const getAgendaStats = query({
  args: { userId: v.string() },
  handler: async (ctx, args) => {
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);

    const appointments = await ctx.db
      .query("appointments")
      .withIndex("by_host", (q) => q.eq("hostId", args.userId))
      .filter((q) => 
        q.and(
          q.gte(q.field("startTime"), startOfMonth.toISOString()),
          q.lte(q.field("startTime"), endOfMonth.toISOString())
        )
      )
      .collect();

    const pendingRequests = await ctx.db
      .query("appointmentRequests")
      .withIndex("by_host", (q) => q.eq("hostId", args.userId))
      .filter((q) => q.eq(q.field("status"), "pending"))
      .collect();

    const stats = {
      totalAppointments: appointments.length,
      completedAppointments: appointments.filter(a => a.status === "completed").length,
      upcomingAppointments: appointments.filter(a => 
        a.status === "scheduled" || a.status === "confirmed"
      ).length,
      cancelledAppointments: appointments.filter(a => a.status === "cancelled").length,
      pendingRequests: pendingRequests.length,
      
      // Por tipo
      appointmentsByType: {
        property_viewing: appointments.filter(a => a.type === "property_viewing").length,
        consultation: appointments.filter(a => a.type === "consultation").length,
        negotiation: appointments.filter(a => a.type === "negotiation").length,
        document_signing: appointments.filter(a => a.type === "document_signing").length,
        other: appointments.filter(a => a.type === "other").length,
      }
    };

    return stats;
  },
}); 